{"name": "vite-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint --fix", "preview": "vite preview"}, "dependencies": {"@heroui/button": "^2.2.24", "@heroui/code": "^2.2.18", "@heroui/dropdown": "^2.3.24", "@heroui/input": "^2.4.25", "@heroui/kbd": "^2.2.19", "@heroui/link": "^2.2.21", "@heroui/navbar": "^2.2.22", "@heroui/snippet": "^2.2.25", "@heroui/switch": "^2.2.22", "@heroui/system": "^2.4.20", "@heroui/theme": "^2.4.20", "@heroui/use-theme": "2.1.10", "@react-aria/visually-hidden": "3.8.26", "@react-types/shared": "3.31.0", "@tailwindcss/postcss": "4.1.11", "@tailwindcss/vite": "4.1.11", "clsx": "2.1.1", "framer-motion": "11.18.2", "react": "18.3.1", "react-dom": "18.3.1", "react-router-dom": "6.23.0", "tailwind-variants": "2.0.1", "tailwindcss": "4.1.11"}, "devDependencies": {"@eslint/compat": "1.2.8", "@eslint/eslintrc": "3.3.1", "@eslint/js": "9.25.1", "@types/node": "20.5.7", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@typescript-eslint/eslint-plugin": "8.31.1", "@typescript-eslint/parser": "8.31.1", "@vitejs/plugin-legacy": "^5.4.3", "@vitejs/plugin-react": "4.7.0", "autoprefixer": "^10.4.21", "eslint": "9.25.1", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-unused-imports": "4.1.4", "globals": "16.0.0", "postcss": "8.5.6", "postcss-preset-env": "^10.2.4", "prettier": "3.5.3", "terser": "^5.43.1", "typescript": "5.6.3", "vite": "6.0.11", "vite-tsconfig-paths": "5.1.4"}}